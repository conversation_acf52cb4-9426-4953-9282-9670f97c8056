# Navigation Menus Collection Schema Documentation

## Overview
The `navigation_menus` collection manages hierarchical website navigation structures with support for multilingual content, parent-child relationships, and flexible menu configurations. This collection enables dynamic menu generation for Hugo sites and provides comprehensive navigation management capabilities.

## Collection Details
- **Collection Name**: `navigation_menus`
- **Collection Type**: Base collection
- **Collection ID**: `pbc_3525715599`
- **Hierarchical Support**: Yes (parent-child relationships)
- **Multilingual Support**: Yes (English and French)
- **Created**: 2025-06-16 15:39:11.180Z
- **Last Modified**: 2025-06-16 15:40:12.540Z

## API Access Rules
- **List Rule**: `@request.auth.id != ""`
- **View Rule**: `@request.auth.id != ""`
- **Create Rule**: `@request.auth.id != "" && (@request.auth.collectionName = "_superusers" || @request.auth.role = "admin" || @request.auth.role = "editor")`
- **Update Rule**: `@request.auth.id != "" && (@request.auth.collectionName = "_superusers" || @request.auth.role = "admin" || @request.auth.role = "editor")`
- **Delete Rule**: `@request.auth.id != "" && (@request.auth.collectionName = "_superusers" || @request.auth.role = "admin")`

## Schema Fields

### Core Navigation Fields
| Field | Type | Required | Max Length | Description |
|-------|------|----------|------------|-------------|
| `menu_name` | select | ✅ | - | Menu location (main, footer, sidebar, mobile) |
| `label` | text | ✅ | 100 | Display text for the menu item |
| `url` | text | ❌ | 500 | Internal URL path |
| `external_url` | url | ❌ | - | External URL (full URL with protocol) |
| `weight` | number | ✅ | 0-9999 | Sort order within menu |
| `language` | select | ✅ | - | Language code (en, fr) |
| `active` | bool | ❌ | - | Whether menu item is active/visible |

### Hierarchical Structure
| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `parent_item` | relation | ❌ | Reference to parent menu item (self-referencing) |
| `menu_depth` | number | ❌ | Depth level (0-3, where 0 is top level) |

### Display & Behavior
| Field | Type | Required | Max Length | Description |
|-------|------|----------|------------|-------------|
| `target` | select | ❌ | - | Link target (_self, _blank, _parent, _top) |
| `css_class` | text | ❌ | 100 | Custom CSS classes for styling |
| `icon` | text | ❌ | 50 | Icon identifier or class name |
| `description` | text | ❌ | 200 | Menu item description or tooltip |

### Advanced Features
| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `visibility_rules` | json | ❌ | Complex visibility conditions (JSON format) |

## Menu Types
- **main**: Primary navigation menu
- **footer**: Footer navigation links
- **sidebar**: Sidebar navigation (if applicable)
- **mobile**: Mobile-specific navigation

## Supported Languages
- **en**: English
- **fr**: French

## Hierarchical Structure Rules
- **Maximum Depth**: 3 levels (0, 1, 2, 3)
- **Parent Validation**: Parent must exist and be in same menu and language
- **Self-Reference**: Collection supports self-referencing for parent-child relationships

## Usage Examples

### Fetch Main Menu (English)
```javascript
// Get main menu items for English
const mainMenuEN = await pb.collection('navigation_menus').getFullList({
  filter: 'menu_name="main" && language="en" && active=true',
  sort: 'weight'
});
```

### Create Menu Item
```javascript
// Create a new menu item
const menuItem = await pb.collection('navigation_menus').create({
  menu_name: "main",
  label: "About Us",
  url: "/about",
  weight: 20,
  language: "en",
  active: true,
  target: "_self",
  menu_depth: 0
});
```

### Create Submenu Item
```javascript
// Create a submenu item with parent reference
const submenuItem = await pb.collection('navigation_menus').create({
  menu_name: "main",
  label: "Our Team",
  url: "/about/team",
  parent_item: parentMenuItemId,
  weight: 10,
  language: "en",
  active: true,
  target: "_self",
  menu_depth: 1
});
```

### Get Hierarchical Menu Structure
```javascript
// Get menu with parent-child relationships
const menuItems = await pb.collection('navigation_menus').getFullList({
  filter: 'menu_name="main" && language="en" && active=true',
  sort: 'weight',
  expand: 'parent_item'
});

// Build hierarchical structure
const buildMenuTree = (items) => {
  const tree = [];
  const itemMap = {};
  
  // Create item map
  items.forEach(item => {
    itemMap[item.id] = { ...item, children: [] };
  });
  
  // Build tree
  items.forEach(item => {
    if (item.parent_item) {
      itemMap[item.parent_item]?.children.push(itemMap[item.id]);
    } else {
      tree.push(itemMap[item.id]);
    }
  });
  
  return tree;
};
```

## Validation Rules
- **Label Length**: Maximum 100 characters
- **URL Length**: Maximum 500 characters
- **Menu Depth**: 0-3 levels only
- **Weight Range**: 0-9999
- **Language**: Must be 'en' or 'fr'
- **Menu Name**: Must be one of: main, footer, sidebar, mobile

## Hugo Integration
The navigation structure is designed to be compatible with Hugo's menu system:

```toml
# Generated Hugo menu structure
[[main]]
name = "About"
url = "/about"
weight = 20

[[main]]
name = "Our Team"
url = "/about/team"
parent = "About"
weight = 10
```

## Default Data
The collection includes sample navigation data based on the existing Hugo menu configuration:
- Main menu items for English and French
- Footer menu items for both languages
- Example hierarchical menu structure
- Proper weight-based ordering

## Performance Considerations
- Index on `menu_name`, `language`, and `active` for efficient filtering
- Consider caching menu structures for high-traffic sites
- Use weight-based ordering for consistent menu display

## Security Notes
- Only authenticated users can view menu items
- Only admins, editors, and superusers can create/update menu items
- Only admins and superusers can delete menu items
- Menu visibility can be controlled via the `active` field and `visibility_rules`
