{"name": "navigation_menus", "type": "base", "system": false, "schema": [{"id": "menu_name", "name": "menu_name", "type": "select", "required": true, "presentable": false, "unique": false, "options": {"maxSelect": 1, "values": ["main", "footer", "sidebar", "mobile"]}}, {"id": "label", "name": "label", "type": "text", "required": true, "presentable": false, "unique": false, "options": {"min": null, "max": 100, "pattern": ""}}, {"id": "url", "name": "url", "type": "text", "required": false, "presentable": false, "unique": false, "options": {"min": null, "max": 500, "pattern": ""}}, {"id": "external_url", "name": "external_url", "type": "url", "required": false, "presentable": false, "unique": false, "options": {"exceptDomains": null, "onlyDomains": null}}, {"id": "parent_item", "name": "parent_item", "type": "relation", "required": false, "presentable": false, "unique": false, "options": {"collectionId": "pbc_3525715599", "cascadeDelete": false, "minSelect": null, "maxSelect": 1, "displayFields": ["label"]}}, {"id": "weight", "name": "weight", "type": "number", "required": true, "presentable": false, "unique": false, "options": {"min": 0, "max": 9999, "noDecimal": false}}, {"id": "language", "name": "language", "type": "select", "required": true, "presentable": false, "unique": false, "options": {"maxSelect": 1, "values": ["en", "fr"]}}, {"id": "active", "name": "active", "type": "bool", "required": false, "presentable": false, "unique": false, "options": {}}, {"id": "target", "name": "target", "type": "select", "required": false, "presentable": false, "unique": false, "options": {"maxSelect": 1, "values": ["_self", "_blank", "_parent", "_top"]}}, {"id": "css_class", "name": "css_class", "type": "text", "required": false, "presentable": false, "unique": false, "options": {"min": null, "max": 100, "pattern": ""}}, {"id": "icon", "name": "icon", "type": "text", "required": false, "presentable": false, "unique": false, "options": {"min": null, "max": 50, "pattern": ""}}, {"id": "description", "name": "description", "type": "text", "required": false, "presentable": false, "unique": false, "options": {"min": null, "max": 200, "pattern": ""}}, {"id": "visibility_rules", "name": "visibility_rules", "type": "json", "required": false, "presentable": false, "unique": false, "options": {"maxSize": 2000000}}, {"id": "menu_depth", "name": "menu_depth", "type": "number", "required": false, "presentable": false, "unique": false, "options": {"min": 0, "max": 3, "noDecimal": false}}], "indexes": [], "listRule": "@request.auth.id != \"\"", "viewRule": "@request.auth.id != \"\"", "createRule": "@request.auth.id != \"\" && (@request.auth.collectionName = \"_superusers\" || @request.auth.role = \"admin\" || @request.auth.role = \"editor\")", "updateRule": "@request.auth.id != \"\" && (@request.auth.collectionName = \"_superusers\" || @request.auth.role = \"admin\" || @request.auth.role = \"editor\")", "deleteRule": "@request.auth.id != \"\" && (@request.auth.collectionName = \"_superusers\" || @request.auth.role = \"admin\")", "options": {}}